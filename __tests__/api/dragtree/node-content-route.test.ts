/**
 * Unit tests for /api/dragtree/node-content route
 */

import type { MockedFunction } from 'jest-mock'

jest.mock('@/auth', () => ({
  __esModule: true,
  auth: jest.fn(),
}))

jest.mock('@/app/libs/rateLimiter', () => ({
  __esModule: true,
  isRateLimited: jest.fn(() => false),
  getRetryAfterSeconds: jest.fn(() => 1),
}))

jest.mock('@/app/server-actions/drag-tree/get-tree-structure', () => ({
  __esModule: true,
  getNodeContentOnDemand: jest.fn(),
}))

jest.mock('next/server', () => ({
  __esModule: true,
  NextResponse: {
    json: (body: any, init?: { status?: number; headers?: Record<string, string> }) => ({
      status: init?.status ?? 200,
      headers: init?.headers || {},
      async json() {
        return body
      },
      body,
    }),
  },
}))

import { GET } from '@/app/api/dragtree/node-content/route'

const { auth } = require('@/auth')
const { isRateLimited, getRetryAfterSeconds } = require('@/app/libs/rateLimiter')
const { getNodeContentOnDemand } = require(
  '@/app/server-actions/drag-tree/get-tree-structure'
)

type AnyFn = (...args: any[]) => any
type AnyMock = MockedFunction<AnyFn>

const asMock = (fn: unknown): AnyMock => fn as AnyMock

const makeReq = (query: string) => ({
  url: `http://localhost/api/dragtree/node-content${query}`,
}) as any

describe('/api/dragtree/node-content GET', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('returns 401 when unauthorized', async () => {
    asMock(auth).mockResolvedValue(null)
    const res: any = await GET(makeReq('?nodeId=n1'))
    expect(res.status).toBe(401)
    const body = await res.json()
    expect(body.error).toBe('Unauthorized')
  })

  it('returns 400 when nodeId is missing', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'u1' } })
    const res: any = await GET(makeReq(''))
    expect(res.status).toBe(400)
    const body = await res.json()
    expect(body.error).toBe('nodeId parameter is required')
  })

  it('returns 429 when rate limited', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'u1' } })
    asMock(isRateLimited).mockReturnValue(true)
    asMock(getRetryAfterSeconds).mockReturnValue(2)
    const res: any = await GET(makeReq('?nodeId=n1'))
    expect(res.status).toBe(429)
    expect(res.headers['Retry-After']).toBe('2')
  })

  it('maps service Unauthorized to 401', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'u1' } })
    asMock(isRateLimited).mockReturnValue(false)
    asMock(getNodeContentOnDemand).mockResolvedValue({
      success: false,
      error: 'Unauthorized',
    })
    const res: any = await GET(makeReq('?nodeId=n1'))
    expect(res.status).toBe(401)
    const body = await res.json()
    expect(body.error).toBe('Unauthorized')
  })

  it('maps service not found to 404', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'u1' } })
    asMock(isRateLimited).mockReturnValue(false)
    asMock(getNodeContentOnDemand).mockResolvedValue({
      success: false,
      error: 'Not found',
    })
    const res: any = await GET(makeReq('?nodeId=n1'))
    expect(res.status).toBe(404)
  })

  it('returns 200 with data and metrics headers on success', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'u1' } })
    asMock(isRateLimited).mockReturnValue(false)
    asMock(getNodeContentOnDemand).mockResolvedValue({
      success: true,
      data: { content_items: [{ id: 'c1' }] },
      metrics: { payloadSize: 1234 },
    })
    const res: any = await GET(makeReq('?nodeId=n1'))
    expect(res.status).toBe(200)
    expect(res.headers['X-Response-Time']).toBeDefined()
    expect(res.headers['X-Payload-Size']).toBe('1234')
    expect(res.headers['X-Content-Items']).toBe('1')
    const body = await res.json()
    expect(body.success).toBe(true)
    expect(body.data.content_items.length).toBe(1)
  })

  it('returns 500 on unexpected error', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'u1' } })
    asMock(isRateLimited).mockReturnValue(false)
    asMock(getNodeContentOnDemand).mockRejectedValue(
      new Error('boom')
    )
    const res: any = await GET(makeReq('?nodeId=n1'))
    expect(res.status).toBe(500)
    const body = await res.json()
    expect(body.error).toBe('Internal server error')
  })
})
