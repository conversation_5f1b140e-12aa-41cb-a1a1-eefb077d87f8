/**
 * Unit tests for /api/dragtree/content route
 * Ensures clear, non-500 responses for common edge cases
 */

import type { MockedFunction } from 'jest-mock'

// Mocks
jest.mock('@/app/libs/prismadb', () => ({
  __esModule: true,
  default: {
    dragTreeNodeContent: {
      findUnique: jest.fn(),
    },
  },
}))

jest.mock('@/auth', () => ({
  __esModule: true,
  auth: jest.fn(),
}))

// Minimal NextResponse.json mock returning a Response-like object
jest.mock('next/server', () => ({
  __esModule: true,
  NextResponse: {
    json: (body: any, init?: { status?: number; headers?: Record<string, string> }) => ({
      status: init?.status ?? 200,
      headers: init?.headers || {},
      async json() {
        return body
      },
      body,
    }),
  },
}))

// Import after mocks so the route sees mocked modules
import { GET } from '@/app/api/dragtree/content/route'

const prisma = require('@/app/libs/prismadb').default
const { auth } = require('@/auth')

type AnyFn = (...args: any[]) => any
type AnyMock = MockedFunction<AnyFn>

const asMock = (fn: unknown): AnyMock => fn as AnyMock

const makeReq = (query: string) => ({ url: `http://localhost/api/dragtree/content${query}` }) as any

describe('/api/dragtree/content GET', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('returns 401 when unauthorized', async () => {
    asMock(auth).mockResolvedValue(null)

    const res: any = await GET(makeReq('?contentId=abc'))
    expect(res.status).toBe(401)
    const body = await res.json()
    expect(body.error).toBe('Unauthorized')
  })

  it('supports prime warm-up with auth', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'user1' } })

    const res: any = await GET(makeReq('?prime=true'))
    expect(res.status).toBe(200)
    const body = await res.json()
    expect(body.success).toBe(true)
    expect(body.prime).toBe(true)
  })

  it('returns 400 when contentId is missing (non-prime)', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'user1' } })

    const res: any = await GET(makeReq(''))
    expect(res.status).toBe(400)
    const body = await res.json()
    expect(body.error).toBe('Content ID is required')
  })

  it('returns 404 when content not found', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'user1' } })
    asMock(prisma.dragTreeNodeContent.findUnique).mockResolvedValue(null)

    const res: any = await GET(makeReq('?contentId=missing'))
    expect(res.status).toBe(404)
    const body = await res.json()
    expect(body.error).toBe('Content not found')
  })

  it('returns 404 when parent drag tree is missing', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'user1' } })
    asMock(prisma.dragTreeNodeContent.findUnique).mockResolvedValue({
      id: 'content1',
      drag_tree: null,
    })

    const res: any = await GET(makeReq('?contentId=content1'))
    expect(res.status).toBe(404)
    const body = await res.json()
    expect(body.error).toBe('Parent drag tree not found')
  })

  it('returns 403 when accessing content owned by another user', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'user1' } })
    asMock(prisma.dragTreeNodeContent.findUnique).mockResolvedValue({
      id: 'content1',
      drag_tree: { id: 'tree1', user_id: 'user2' },
    })

    const res: any = await GET(makeReq('?contentId=content1'))
    expect(res.status).toBe(403)
    const body = await res.json()
    expect(body.error).toBe('Access denied')
  })

  it('returns 200 with content for owner', async () => {
    asMock(auth).mockResolvedValue({ user: { id: 'user1' } })
    asMock(prisma.dragTreeNodeContent.findUnique).mockResolvedValue({
      id: 'content1',
      content_text: 'Hello world',
      messages: [],
      content_metadata: {},
      drag_tree: { id: 'tree1', user_id: 'user1' },
    })

    const res: any = await GET(makeReq('?contentId=content1'))
    expect(res.status).toBe(200)
    const body = await res.json()
    expect(body.success).toBe(true)
    expect(body.data?.id).toBe('content1')
    expect(body.data?.content_text).toBe('Hello world')
  })
})
