/**
 * Feature Flags Configuration
 *
 * This file contains boolean flags to control feature rollouts and A/B testing.
 * These are compile-time constants, not environment variables.
 */

// =============================================================================
// CHAT SYSTEM FLAGS
// =============================================================================

/**
 * Enable Chat v2 System
 *
 * When true: Uses ChatTabContentV2 with improved API architecture
 * When false: Uses ChatTabContent (v1) with legacy architecture
 *
 * Chat v2 Benefits:
 * - API handles message history retrieval internally
 * - Reduced payload size for long conversations
 * - Better separation of concerns
 * - Proper system message handling for context
 * - AI SDK v5 native support with tool call display
 */
export const ENABLE_CHAT_V2 = true

/**
 * Enable Chat v2 Enhanced Persistence
 *
 * When true: Uses optimized persistence with conversation history reconstruction
 * When false: Uses simplified persistence (current v2 implementation)
 */
export const ENABLE_CHAT_V2_ENHANCED_PERSISTENCE = true

// =============================================================================
// AI PANE FLAGS
// =============================================================================

/**
 * Enable AI Pane Chat v2
 *
 * Controls which chat system is used when starting new AI Pane chats
 */
export const ENABLE_AI_PANE_CHAT_V2 = true

// =============================================================================
// RESEARCH SYSTEM FLAGS
// =============================================================================

/**
 * Enable Two-Stage Research System
 *
 * When true: Uses two-stage research generation (template assessment + custom flow)
 * When false: Uses existing single-template research approach
 *
 * Two-Stage Research Benefits:
 * - Template assessment analyzes research question suitability
 * - Custom flow generation for specialized research scenarios
 * - Better research quality for diverse question types
 * - Maintains backward compatibility with existing template
 */
export const ENABLE_TWO_STAGE_RESEARCH = true

/**
 * Enable OpenAI native web search for quick research (v2)
 *
 * When true: backend uses OpenAI Responses API with web_search_preview; frontend shows Steps UI
 * When false: backend uses legacy Brave search tools; frontend shows citations UI
 *
 * IMPORTANT: Use this single flag everywhere instead of duplicating per-file flags.
 */
export const ENABLE_OPENAI_NATIVE_WEB_SEARCH = true

/**
 * Select which quick research route to use on the frontend
 * true  => use v2 route (/api/dragtree/research_generate_v2)
 * false => use v1 route (/api/dragtree/research_generate)
 *
 * NOTE: This flag is consumed ONLY by the frontend to choose which
 * API endpoint to call. Backend behavior is determined by the
 * implementation inside each route file, not by this flag.
 */
export const USE_RESEARCH_ROUTE_V2 = true

// =============================================================================
// DEVELOPMENT FLAGS
// =============================================================================

/**
 * Enable Debug Logging for Chat Systems
 *
 * When true: Enables detailed console logging for chat operations
 * When false: Uses production logging levels
 */
export const ENABLE_CHAT_DEBUG_LOGGING = true // Temporarily enabled for debugging

/**
 * Enable Performance Monitoring
 *
 * When true: Tracks and logs performance metrics for chat operations
 * When false: Skips performance tracking
 */
export const ENABLE_CHAT_PERFORMANCE_MONITORING = true

// =============================================================================
// FEATURE FLAG UTILITIES
// =============================================================================

// =============================================================================
// DRAGTREE VISUAL FLOW FLAGS
// =============================================================================
/**
 * Enable ReactFlow Issue Tree (hierarchical + radial) diagram modes
 * This controls whether the ReactFlow-based Issue Tree views are available.
 * Default: enabled (requested as default primary view alongside 3D graph).
 */
export const ENABLE_DRAGTREE_ISSUE_TREE: boolean = true

/**
 * Enable the radial (circular) layout inside Issue Tree modes
 * When false, users can only use hierarchical (linear) layout.
 * Default: enabled.
 */
export const ENABLE_DRAGTREE_ISSUE_TREE_RADIAL: boolean = true

/**
 * Enable 2D force graph view
 * Default per request: do not show 2D force graph.
 */
export const ENABLE_DRAGTREE_FORCE_GRAPH_2D: boolean = false

/**
 * Enable 3D force graph view
 * Default per request: show 3D force graph.
 */
export const ENABLE_DRAGTREE_FORCE_GRAPH_3D: boolean = true

/**
 * Enable node selection mode in Issue Tree (shows selection UI and logic)
 * When false, the "Select to Use" UI and related selection logic are hidden.
 */
export const ENABLE_DRAGTREE_NODE_SELECTION: boolean = false

/**
 * Get the current chat system version based on feature flags
 */
export const getChatSystemVersion = (): 'v1' | 'v2' => {
  return ENABLE_CHAT_V2 ? 'v2' : 'v1'
}

/**
 * Get the API endpoint for the current chat system
 */
export const getChatApiEndpoint = (): string => {
  return ENABLE_CHAT_V2 ? '/api/aipane/chat-v2' : '/api/aipane/chat'
}

/**
 * Check if enhanced features are enabled for the current chat system
 */
export const isChatEnhancedFeaturesEnabled = (): boolean => {
  return ENABLE_CHAT_V2 && ENABLE_CHAT_V2_ENHANCED_PERSISTENCE
}

/**
 * Get feature flag status for debugging
 */
export const getFeatureFlagStatus = () => {
  return {
    chatV2: ENABLE_CHAT_V2,
    chatV2EnhancedPersistence: ENABLE_CHAT_V2_ENHANCED_PERSISTENCE,
    aiPaneChatV2: ENABLE_AI_PANE_CHAT_V2,
    twoStageResearch: ENABLE_TWO_STAGE_RESEARCH,
    debugLogging: ENABLE_CHAT_DEBUG_LOGGING,
    performanceMonitoring: ENABLE_CHAT_PERFORMANCE_MONITORING,
    currentChatSystem: getChatSystemVersion(),
    currentApiEndpoint: getChatApiEndpoint(),
  }
}

// =============================================================================
// FEATURE FLAG VALIDATION
// =============================================================================

/**
 * Validate feature flag configuration at startup
 * This helps catch configuration issues early
 */
export const validateFeatureFlags = (): {
  valid: boolean
  errors: string[]
} => {
  const errors: string[] = []

  // Validate chat system flags
  if (ENABLE_CHAT_V2_ENHANCED_PERSISTENCE && !ENABLE_CHAT_V2) {
    errors.push(
      'ENABLE_CHAT_V2_ENHANCED_PERSISTENCE requires ENABLE_CHAT_V2 to be true'
    )
  }

  if (ENABLE_AI_PANE_CHAT_V2 && !ENABLE_CHAT_V2) {
    errors.push('ENABLE_AI_PANE_CHAT_V2 requires ENABLE_CHAT_V2 to be true')
  }

  return {
    valid: errors.length === 0,
    errors,
  }
}

// Log feature flag status in development
if (ENABLE_CHAT_DEBUG_LOGGING) {
  console.log(
    '🚩 [Feature Flags] Current configuration:',
    getFeatureFlagStatus()
  )

  const validation = validateFeatureFlags()
  if (!validation.valid) {
    console.error('❌ [Feature Flags] Configuration errors:', validation.errors)
  } else {
    console.log('✅ [Feature Flags] Configuration is valid')
  }
}
