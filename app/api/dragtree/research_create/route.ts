import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import { createDragTreeNodeContent } from '@/app/server-actions/drag-tree'
import { DragTreeNodeContentStatus, SubscriptionTier } from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { getQuickResearchLimit } from '@/app/configs/tier-permissions'

export const maxDuration = 30

export type CreateResearchContentRequest = {
  dragTreeNodeId: string
  questionText: string
  researchType?: string
}

export async function POST(req: NextRequest) {
  try {
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const {
      dragTreeNodeId,
      questionText,
      researchType = 'QUICK_RESEARCH',
    }: CreateResearchContentRequest = await req.json()

    if (!dragTreeNodeId || !questionText) {
      return NextResponse.json(
        { error: 'Missing required fields: dragTreeNodeId, questionText' },
        { status: 400 }
      )
    }

    // Get the dragTreeId from the node (we need this for the content creation)
    const dragTreeNode = await prisma.dragTreeNode.findUnique({
      where: { id: dragTreeNodeId },
      select: { drag_tree_id: true },
    })

    if (!dragTreeNode) {
      return NextResponse.json(
        { error: 'DragTree node not found' },
        { status: 404 }
      )
    }

    // Enforce per-tier quick research limit per drag tree
    try {
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        select: { subscription_tier: true },
      })
      const tier = user?.subscription_tier || SubscriptionTier.FREE
      const quickLimit = getQuickResearchLimit(tier)

      if (quickLimit > 0) {
        const existingCount = await prisma.dragTreeNodeContent.count({
          where: {
            drag_tree_id: dragTreeNode.drag_tree_id,
            content_type: 'QUICK_RESEARCH',
          },
        })

        if (existingCount >= quickLimit) {
          return NextResponse.json(
            {
              success: false,
              error:
                tier === SubscriptionTier.FREE
                  ? `Free plan limit reached: You can create up to ${quickLimit} quick research items per drag tree. Consider upgrading to PRO.`
                  : 'You have reached the quick research limit for your plan.',
              code: 'QUICK_RESEARCH_LIMIT_REACHED',
              limit: quickLimit,
              currentCount: existingCount,
            },
            { status: 403 }
          )
        }
      }
    } catch (e) {
      // Non-fatal. If limit check fails, proceed but log.
      console.warn('Failed to enforce quick research limit', e)
    }

    // Create content record with INITIALIZED status
    const result = await createDragTreeNodeContent({
      dragTreeId: dragTreeNode.drag_tree_id,
      dragTreeNodeId: dragTreeNodeId,
      contentType: researchType as 'QUICK_RESEARCH',
      contentVersion: 'v1',
      contentText: '', // Empty initially
      contentMetadata: {
        questionText,
        createdBy: session.user.id,
        researchInitiatedAt: new Date().toISOString(),
      },
      messages: [],
      generationMetadata: {
        researchType,
        originalQuestion: questionText,
      },
    })

    if (!result.success || !result.data) {
      return NextResponse.json(
        { error: result.error || 'Failed to create research content' },
        { status: 500 }
      )
    }

    console.log(
      `🔬 [Research] Created content record: ${result.data.id} for node: ${dragTreeNodeId}`
    )

    return NextResponse.json({
      success: true,
      data: {
        contentId: result.data.id,
        status: result.data.status,
        dragTreeNodeId: dragTreeNodeId,
        questionText,
      },
    })
  } catch (error) {
    console.error('❌ [Research Create] Error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PATCH(request: Request) {
  try {
    console.log('🔄 [Research Create API] PATCH request received')

    const { contentId, status } = await request.json()

    if (!contentId || !status) {
      console.error('❌ [Research Create API] Missing contentId or status')
      return NextResponse.json(
        { success: false, error: 'Missing contentId or status' },
        { status: 400 }
      )
    }

    console.log(
      `🔄 [Research Create API] Updating content ${contentId} to status ${status}`
    )

    // Update the content status in database
    const updatedContent = await prisma.dragTreeNodeContent.update({
      where: { id: contentId },
      data: { status: status as DragTreeNodeContentStatus },
    })

    console.log('✅ [Research Create API] Successfully updated content status')

    return NextResponse.json({
      success: true,
      data: { contentId: updatedContent.id, status: updatedContent.status },
    })
  } catch (error) {
    console.error('❌ [Research Create API] PATCH error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to update content status' },
      { status: 500 }
    )
  }
}
