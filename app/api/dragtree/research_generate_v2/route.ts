import { streamText, stepCountIs } from 'ai'
import { openai } from '@ai-sdk/openai'
import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@/auth'
import {
  DragTreeNodeContentStatus,
  Prisma,
  AIUsageType,
  DragTreeNodeContentType,
} from '@prisma/client'
import prisma from '@/app/libs/prismadb'
import { createAIUsage } from '@/app/server-actions/log_ai_usage'
import {
  createResearchPrompt,
  createCustomTemplatePrompt,
} from '@/app/api/dragtree/shared/research-prompts'
import {
  assessTemplate,
  DECISION_ANALYST_TEMPLATE,
} from '@/app/api/dragtree/shared/assess-template'
import { ENABLE_TWO_STAGE_RESEARCH } from '@/app/configs/feature-flags'
import {
  getModelConfigFromSession,
  getProviderNameForUsage,
} from '@/app/libs/model-config'
import {
  getLanguageName,
  type SupportedLanguageCode,
} from '@/app/constants/languages'
import { researchGenerateSchema } from '@/app/libs/api-schemas'
import { validateRequestBody } from '@/app/libs/validation-utils'
import { extractTextContent } from '@/app/types/ai-sdk5'
import { enforceRateLimit } from '@/app/libs/llmRateLimit'

export const maxDuration = 300

// Builds the system message, optionally using two-step assessment when enabled
async function buildSystemMessage(params: {
  userId: string
  contentId: string
  screeningQuestion: string
  questionText: string
  language: string
}): Promise<string> {
  const { userId, contentId, screeningQuestion, questionText, language } =
    params

  if (!ENABLE_TWO_STAGE_RESEARCH) {
    return createResearchPrompt(questionText, screeningQuestion, language)
  }

  try {
    const assessment = await assessTemplate(
      userId,
      contentId,
      screeningQuestion || '',
      questionText
    )

    if (assessment && assessment.suitable) {
      return createCustomTemplatePrompt(
        DECISION_ANALYST_TEMPLATE,
        questionText,
        screeningQuestion,
        language
      )
    }

    if (assessment && !assessment.suitable && assessment.customTemplate) {
      return createCustomTemplatePrompt(
        assessment.customTemplate,
        questionText,
        screeningQuestion,
        language
      )
    }
  } catch {}

  return createResearchPrompt(questionText, screeningQuestion, language)
}

function toJsonValue(value: unknown): Prisma.InputJsonValue {
  return value as Prisma.InputJsonValue
}

export async function POST(req: NextRequest) {
  let contentId: string | undefined

  try {
    const session = await auth()
    const userId = session?.user?.id
    if (!userId)
      return NextResponse.json({ message: 'Unauthorized' }, { status: 401 })

    // Distributed rate-limit per user & route (KV-backed if configured)
    const rl = await enforceRateLimit(
      session,
      ENABLE_TWO_STAGE_RESEARCH
        ? 'dragtree_2steps_research_generate_v2'
        : 'dragtree_research_generate_v2'
    )
    if (rl) return rl

    const validationResult = await validateRequestBody(
      req,
      researchGenerateSchema
    )
    if (!validationResult.success) return validationResult.error

    const requestData = validationResult.data
    contentId = requestData.contentId
    if (!contentId)
      return NextResponse.json(
        { error: 'Missing required field: contentId' },
        { status: 400 }
      )

    const nodeContent = await prisma.dragTreeNodeContent.findUnique({
      where: { id: contentId },
      select: {
        id: true,
        drag_tree_node: {
          select: {
            label: true,
            drag_tree: {
              select: { id: true, preferred_language: true, user_prompt: true },
            },
          },
        },
      },
    })

    if (!nodeContent?.drag_tree_node?.drag_tree)
      throw new Error('Drag tree not found for content')
    const dragTree = nodeContent.drag_tree_node.drag_tree
    const questionText = nodeContent.drag_tree_node.label
    if (!questionText?.trim())
      return NextResponse.json(
        { error: 'Question text not found in drag tree node label' },
        { status: 400 }
      )

    const researchType: DragTreeNodeContentType =
      DragTreeNodeContentType.QUICK_RESEARCH
    const preferredLanguage =
      (dragTree.preferred_language as SupportedLanguageCode) || 'en'
    const language = getLanguageName(preferredLanguage)
    const screeningQuestion = dragTree.user_prompt || ''

    // Build system message with optional two-step assessment
    const systemMessage = await buildSystemMessage({
      userId,
      contentId,
      screeningQuestion,
      questionText,
      language,
    })

    await prisma.dragTreeNodeContent.update({
      where: { id: contentId },
      data: { status: DragTreeNodeContentStatus.PROCESSING },
    })

    // Resolve model configuration from tier for v2 route
    const modelConfig = await getModelConfigFromSession(
      session,
      ENABLE_TWO_STAGE_RESEARCH
        ? 'dragtree_2steps_research_generate_v2'
        : 'dragtree_research_generate_v2'
    )
    const toolCfg = modelConfig.tools?.openai_web_search_preview
    const REASONING_EFFORT =
      (modelConfig.providerOptions as any)?.openai?.reasoningEffort || 'low'
    const MAX_STEPS = 8

    const providerOptions = {
      openai: {
        reasoningEffort: REASONING_EFFORT,
        reasoningSummary:
          (modelConfig.providerOptions as any)?.openai?.reasoningSummary ||
          'auto',
        textVerbosity:
          (modelConfig.providerOptions as any)?.openai?.textVerbosity || 'low',
        store: true,
        metadata: {
          feature: 'quick-research-v2',
          contentId,
          timestamp: new Date().toISOString(),
        },
      },
    }

    let runUsage: any = undefined
    let finalTextFromRun: string = ''

    const tools = toolCfg?.enabled
      ? {
          web_search_preview: openai.tools.webSearchPreview({
            searchContextSize:
              (toolCfg?.searchContextSize as 'low' | 'medium' | 'high') ||
              'medium',
          }),
        }
      : undefined

    const result = streamText({
      model: openai.responses(modelConfig.model),
      messages: [{ role: 'system', content: systemMessage }],
      tools,
      toolChoice: toolCfg?.enabled ? 'auto' : 'none',
      temperature: modelConfig.temperature,
      maxOutputTokens: modelConfig.maxOutputTokens || 4000,
      stopWhen: stepCountIs(MAX_STEPS),
      providerOptions,
      // Capture usage and final text, but defer persistence to UI stream onFinish
      onFinish: async ({ text, usage }) => {
        runUsage = usage
        finalTextFromRun = (typeof text === 'string' ? text : '').trim()
      },
    })

    return result.toUIMessageStreamResponse({
      onFinish: async ({ messages: uiMessages, responseMessage }) => {
        try {
          // Preserve existing isRead; avoid duplicating messages in content_metadata
          const existingMeta = await prisma.dragTreeNodeContent.findUnique({
            where: { id: contentId },
            select: { content_metadata: true },
          })
          const prevIsRead =
            typeof existingMeta?.content_metadata === 'object' &&
            existingMeta?.content_metadata !== null &&
            (existingMeta!.content_metadata as any).isRead === true

          const contentMetadata: Prisma.InputJsonValue = {
            ...(prevIsRead ? { isRead: true } : {}),
          }

          // Prefer extracting text from UI responseMessage; fallback to captured run text
          const uiFinalText = responseMessage
            ? extractTextContent(responseMessage as any)
            : ''
          const finalText = (uiFinalText || finalTextFromRun || '').trim()

          await Promise.all([
            prisma.dragTreeNodeContent.update({
              where: { id: contentId },
              data: {
                content_text: finalText,
                content_metadata: contentMetadata,
                status: DragTreeNodeContentStatus.ACTIVE,
                // Persist full UIMessage list (with parts/toolInvocations)
                messages: toJsonValue(uiMessages),
              },
            }),
            createAIUsage({
              userId,
              entityType: 'drag_tree_node_content',
              entityId: contentId!,
              aiProvider: getProviderNameForUsage(modelConfig),
              modelName: modelConfig.model,
              usageType: AIUsageType.NODE_QUICK_RESEARCH,
              inputPrompt: systemMessage,
              // Assistant-only output for audit purposes
              messages: [{ role: 'assistant', content: finalText }],
              metadata: {
                tokenUsage: runUsage,
                implementation: 'openai_native_v2',
                language: preferredLanguage,
                researchType,
                providerOptions: {
                  openai: { reasoningEffort: REASONING_EFFORT },
                },
              },
              config: {
                maxSteps: MAX_STEPS,
                reasoningEffort: REASONING_EFFORT,
              },
            }),
          ])
        } catch (error) {
          try {
            await prisma.dragTreeNodeContent.update({
              where: { id: contentId },
              data: { status: DragTreeNodeContentStatus.INACTIVE },
            })
          } catch {}
        }
      },
    })
  } catch (error) {
    if (typeof contentId === 'string') {
      try {
        await prisma.dragTreeNodeContent.update({
          where: { id: contentId },
          data: { status: DragTreeNodeContentStatus.INACTIVE },
        })
      } catch {}
    }
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
