'use client'

import { motion } from 'framer-motion'
import IssueTreeVisualization from '@/app/(landing)/components/issue-tree-visualization'
import { useMemo, useState } from 'react'
import AuthForm from '@/app/(landing)/components/AuthForm'
import {
  Reasoning,
  ReasoningContent,
  ReasoningTrigger,
} from '@/components/ai-elements/reasoning'
import { Response } from '@/components/ai-elements/response'

const CHALLENGES = [
  'Launch a B2B SaaS product in Europe',
  'Enter the US healthcare market',
  'Reduce churn by 20% in 6 months',
  'Scale marketing to 1,000 qualified leads/month',
]

export default function SolutionSection() {
  const [selected, setSelected] = useState<string>(CHALLENGES[0])
  // Single hardcoded example for Step 3 to avoid pre-generating all leaves
  const [isStreaming, setIsStreaming] = useState<boolean>(false)
  const [streamText, setStreamText] = useState<string>('')
  const researchBullets = useMemo(() => {
    if (selected.includes('SaaS')) {
      return [
        '• European B2B SaaS market valued at €45B in 2024',
        '• Growing at 12% CAGR through 2028',
        '• Key markets: Germany, UK, France, Netherlands',
      ]
    }
    if (selected.includes('healthcare')) {
      return [
        '• US healthcare spend >17% GDP; complex payer/provider landscape',
        '• HIPAA compliance required; state-by-state nuances',
        '• Buyers: payers, providers, employers; long sales cycles',
      ]
    }
    if (selected.includes('churn')) {
      return [
        '• Top churn drivers: onboarding friction, weak activation',
        '• Cohorts show risk after month 2; NPS < 30 correlates with churn',
        '• Add success loops: QBRs, health scores, targeted win-backs',
      ]
    }
    return [
      '• CPL benchmarks by channel and region',
      '• Paid vs organic efficiency crossover at ~600 MQLs',
      '• Optimize creative velocity; test cadence weekly',
    ]
  }, [selected])

  return (
    <section
      id="features"
      className="min-h-screen py-20 px-6 bg-slate-950 text-slate-50 flex items-center"
    >
      <div className="max-w-6xl mx-auto">
        <motion.h3
          className="text-h3 text-slate-50 text-center mb-2"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          From Fog to Finish Line. Experience it Now.
        </motion.h3>
        <p className="text-center text-slate-400 mb-16">
          This is a simplified, interactive preview.
        </p>

        {/* Step 1 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-24">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h4 className="text-2xl font-semibold text-slate-50 mb-6">
              1. Start with a real-world challenge.
            </h4>
            <p className="text-body-reduced text-slate-400">
              No blank pages. Select one of these common high-stakes goals to
              see how ThoughtAtlas builds your strategic blueprint.
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <div className="bg-slate-950 rounded-lg p-6 border border-slate-800">
                <div className="text-slate-400 text-sm mb-2">
                  What's your challenge?
                </div>
                <div className="relative">
                  <input
                    type="text"
                    value={selected}
                    readOnly
                    className="w-full bg-transparent text-slate-50 text-lg font-medium border-none outline-none"
                  />
                  <div className="mt-3 grid gap-2">
                    {CHALLENGES.map(c => (
                      <button
                        key={c}
                        onClick={() => setSelected(c)}
                        className={`text-left text-sm px-3 py-2 rounded-md border transition-colors ${
                          c === selected
                            ? 'border-blue-500 bg-blue-500/10 text-blue-300'
                            : 'border-slate-800 hover:border-slate-700 text-slate-400 hover:text-slate-200'
                        }`}
                      >
                        {c}
                      </button>
                    ))}
                    <div className="pt-2">
                      <div className="text-xs text-slate-500 mb-2">
                        Ready to try it?
                      </div>
                      <div className="rounded-md border border-blue-500/30 bg-blue-500/10 p-2 inline-flex">
                        <AuthForm />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>

        {/* Step 2 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center mb-24">
          <motion.div
            className="lg:order-2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h4 className="text-2xl font-semibold text-slate-50 mb-6">
              2. Instantly receive your strategic map.
            </h4>
            <p className="text-body-reduced text-slate-400">
              This isn't a template. It's a research-grade, comprehensive issue
              tree generated in minutes. It's the intellectual net for your
              thinking, ensuring every angle is covered.
            </p>
          </motion.div>
          <motion.div
            className="lg:order-1"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <IssueTreeVisualization challenge={selected} />
          </motion.div>
        </div>

        {/* Step 3 */}
        <div className="grid lg:grid-cols-2 gap-16 items-center">
          <motion.div
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h4 className="text-2xl font-semibold text-slate-50 mb-6">
              3. Get a head start with focused research.
            </h4>
            <p className="text-body-reduced text-slate-400">
              Stop random prompting. Every question is a hook. With one click,
              you cast a targeted research net to capture the essential insights
              needed to move forward.
            </p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="bg-slate-900 rounded-2xl p-8 border border-slate-800">
              <div className="bg-slate-950 rounded-lg p-6 border border-slate-800">
                <h5 className="text-slate-50 font-semibold mb-4">
                  Example Insights
                </h5>
                <div className="space-y-3">
                  <button
                    onClick={() => {
                      setIsStreaming(true)
                      setStreamText('')
                      const question = 'Market Size Analysis'
                      const chunks = [
                        `Analyzing ${question}...`,
                        'Gathering relevant market data and benchmarks...',
                        'Synthesizing competitive landscape and buyer needs...',
                        'Drafting focused insights and next actions...',
                      ]
                      let i = 0
                      const interval = setInterval(() => {
                        setStreamText(
                          prev => (prev ? prev + '\n' : '') + chunks[i]
                        )
                        i++
                        if (i >= chunks.length) {
                          clearInterval(interval)
                          setTimeout(() => setIsStreaming(false), 400)
                        }
                      }, 600)
                    }}
                    className="w-full text-left px-3 py-2 rounded-md border border-slate-800 bg-slate-900 hover:bg-slate-800 text-slate-300"
                  >
                    Research: Market Size Analysis
                  </button>
                  <Reasoning
                    isStreaming={isStreaming}
                    defaultOpen={false}
                    variant="subtle"
                  >
                    <ReasoningTrigger />
                    <ReasoningContent>
                      {streamText ||
                        'Click the question above to simulate research streaming...'}
                    </ReasoningContent>
                  </Reasoning>
                  <div className="rounded-md border border-slate-800 p-3">
                    <Response className="text-sm text-slate-300">
                      {streamText
                        ? `• ${streamText.split('\n').join('\n• ')}`
                        : researchBullets.join('\n')}
                    </Response>
                  </div>
                </div>
                <div className="mt-4 pt-4 border-t border-slate-800">
                  <div className="text-xs text-slate-400">
                    Sources vary by topic; examples only
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
