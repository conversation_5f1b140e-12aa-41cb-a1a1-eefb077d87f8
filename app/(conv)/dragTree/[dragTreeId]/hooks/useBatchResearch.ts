import { useState, useCallback, useEffect } from 'react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { NodeContentType } from '@/app/(conv)/dragTree/[dragTreeId]/constants/node-types'
import { DragTreeNodeContentStatus } from '@prisma/client'
import toast from 'react-hot-toast'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { MAX_BATCH_ITEMS } from '@/app/(conv)/dragTree/constants'
import { useSession } from 'next-auth/react'
import { SubscriptionTier } from '@prisma/client'
import {
  getBatchResearchLimit,
  getQuickResearchLimit,
} from '@/app/configs/tier-permissions'
import { useBatchResearchStore } from '@/app/stores/batchResearchStore'
import { logEventWithContext } from '@/app/libs/logging'

// Types for batch research state management
export type BatchResearchItem = {
  nodeId: string
  questionText: string
  displayText: string
  categoryPath: string[]
  categoryId: string
  hasExistingResearch: boolean
  status: 'pending' | 'processing' | 'completed' | 'failed'
  error?: string
}

export type CategoryGroup = {
  categoryId: string
  categoryName: string
  categoryPath: string[]
  items: BatchResearchItem[]
}

export type HierarchicalCategory = {
  name: string
  level: number
  path: string[]
  children: Map<string, HierarchicalCategory>
  items: BatchResearchItem[]
}

export type BatchResearchState = {
  isOpen: boolean
  items: BatchResearchItem[]
  categoryGroups: CategoryGroup[]
  hierarchicalStructure: HierarchicalCategory[]
  selectedItems: Set<string>
  selectedCategories: Set<string>
  isProcessing: boolean
  processedCount: number
  totalCount: number
  currentProcessingId: string | null
  // Coach mode state - FEATURE TEMPORARILY DISABLED
  // Complete implementation preserved for potential future use
  isCoachMode: boolean
  currentQuestionIndex: number
  coachModeTotal: number // total questions available when coach mode started (stable)
}

/**
 * Hook for managing batch research operations
 * Handles queue management, rate limiting, and API calls
 */
export const useBatchResearch = (dragTreeId: string = '') => {
  const { data: session } = useSession()
  const tier =
    (session?.user as any)?.subscription?.tier ||
    (session?.user as any)?.subscription_tier ||
    SubscriptionTier.FREE
  const tierBatchLimit = getBatchResearchLimit(tier)
  const quickPerTreeLimit = getQuickResearchLimit(tier)
  const { navigateToTreeNode } = useNavigationStore()

  // Persistent store integration
  const {
    getSelections,
    setSelections,
    toggleSelection,
    isCoachModeOpen,
    setCoachModeOpen,
    setCurrentQuestionId,
  } = useBatchResearchStore()

  const [state, setState] = useState<BatchResearchState>({
    isOpen: false,
    items: [],
    categoryGroups: [],
    hierarchicalStructure: [],
    selectedItems: new Set(),
    selectedCategories: new Set(),
    isProcessing: false,
    processedCount: 0,
    totalCount: 0,
    currentProcessingId: null,
    // Coach mode initial state
    isCoachMode: false,
    currentQuestionIndex: 0,
    coachModeTotal: 0,
  })

  // Initialize state from persistent store only once
  useEffect(() => {
    if (dragTreeId) {
      const persistentSelections = getSelections(dragTreeId)
      const coachModeState = isCoachModeOpen(dragTreeId)
      setState(prev => ({
        ...prev,
        selectedItems: persistentSelections,
        isCoachMode: coachModeState,
      }))
    }
  }, [dragTreeId]) // Only depend on dragTreeId to prevent infinite loops

  // Select only the slices that this hook actually needs
  const frontendTreeStructure = useDragTreeStore(
    state => state.frontendTreeStructure
  )
  const getNodeContent = useDragTreeStore(state => state.getNodeContent)
  const markNodeAsInterested = useDragTreeStore(
    state => state.markNodeAsInterested
  )
  const addNodeContent = useDragTreeStore(state => state.addNodeContent)

  /**
   * Recursively find all questions and organize them by category
   * Now includes both researchable and completed questions for better UX
   */
  const findAllQuestions = useCallback(
    (
      node: any,
      parentPath: string[] = [],
      parentCategoryId: string = 'root'
    ): BatchResearchItem[] => {
      const items: BatchResearchItem[] = []

      if (node.type === 'question') {
        // Check if this node already has research content
        const nodeContentMap = getNodeContent(node.id)
        const hasExistingContent = nodeContentMap && nodeContentMap.size > 0

        // Get the actual question text from the node
        const questionText = node.text || node.label || 'Untitled Question'

        items.push({
          nodeId: node.id,
          questionText,
          displayText: questionText, // Use question text directly, not full path
          categoryPath: parentPath,
          categoryId: parentCategoryId,
          hasExistingResearch: hasExistingContent || false,
          status: hasExistingContent ? 'completed' : 'pending',
        })
      }

      // Recursively check children
      if (node.children && Array.isArray(node.children)) {
        const currentPath =
          node.type === 'category'
            ? [...parentPath, node.text || node.label || 'Untitled Category']
            : parentPath
        const currentCategoryId =
          node.type === 'category' ? node.id : parentCategoryId

        node.children.forEach((child: any) => {
          items.push(...findAllQuestions(child, currentPath, currentCategoryId))
        })
      }

      return items
    },
    [getNodeContent]
  )

  /**
   * Group questions by category and maintain tree order
   */
  const groupQuestionsByCategory = useCallback(
    (items: BatchResearchItem[]): CategoryGroup[] => {
      const categoryMap = new Map<string, CategoryGroup>()
      const categoryOrder = new Map<string, number>()
      let orderIndex = 0

      // First pass: collect all categories and establish order
      items.forEach(item => {
        const categoryId = item.categoryId

        if (!categoryMap.has(categoryId)) {
          // Use full category path as display name, not just the last part
          const categoryName =
            item.categoryPath.length > 0
              ? item.categoryPath.join(' > ') // Show full hierarchy
              : 'Root Questions'

          categoryMap.set(categoryId, {
            categoryId,
            categoryName,
            categoryPath: item.categoryPath,
            items: [],
          })

          // Set order based on first encounter (maintains tree traversal order)
          categoryOrder.set(categoryId, orderIndex++)
        }

        categoryMap.get(categoryId)!.items.push(item)
      })

      // Return categories in the order they were encountered during tree traversal
      return Array.from(categoryMap.values()).sort((a, b) => {
        const orderA = categoryOrder.get(a.categoryId) ?? 0
        const orderB = categoryOrder.get(b.categoryId) ?? 0
        return orderA - orderB
      })
    },
    []
  )

  /**
   * Create hierarchical structure for visual display with separators
   */
  const createHierarchicalStructure = useCallback(
    (items: BatchResearchItem[]): HierarchicalCategory[] => {
      const root = new Map<string, HierarchicalCategory>()

      // Build the tree structure
      items.forEach(item => {
        if (item.categoryPath.length === 0) return // Skip root questions

        let currentLevel = root
        let currentPath: string[] = []

        // Navigate/create the hierarchy
        item.categoryPath.forEach((categoryName, index) => {
          currentPath = [...currentPath, categoryName]

          if (!currentLevel.has(categoryName)) {
            currentLevel.set(categoryName, {
              name: categoryName,
              level: index,
              path: currentPath,
              children: new Map(),
              items: [],
            })
          }

          const category = currentLevel.get(categoryName)!

          // If this is the final level, add the item
          if (index === item.categoryPath.length - 1) {
            category.items.push(item)
          }

          currentLevel = category.children
        })
      })

      // Convert to flat array for easier rendering
      const flattenHierarchy = (
        categories: Map<string, HierarchicalCategory>
      ): HierarchicalCategory[] => {
        const result: HierarchicalCategory[] = []

        Array.from(categories.values()).forEach(category => {
          result.push(category)
          // Add children recursively
          if (category.children.size > 0) {
            result.push(...flattenHierarchy(category.children))
          }
        })

        return result
      }

      return flattenHierarchy(root)
    },
    []
  )

  /**
   * Open batch research dialog and populate with available questions
   */
  const openBatchResearch = useCallback(() => {
    if (!frontendTreeStructure) {
      toast.error('No tree structure available')
      return
    }

    const allItems = findAllQuestions(frontendTreeStructure)

    // Filter out root-level questions (questions without a proper category path)
    // This forces users to select subcategories instead of selecting everything at once
    const filteredItems = allItems.filter(item => {
      // Only include questions that have a meaningful category path (not root)
      return item.categoryPath.length > 0
    })

    const categoryGroups = groupQuestionsByCategory(filteredItems)
    const hierarchicalStructure = createHierarchicalStructure(filteredItems)

    if (filteredItems.length === 0) {
      toast.success('No questions found in tree structure')
      return
    }

    // Don't select anything by default - add friction to make users read questions
    // This prevents users from blindly selecting all questions for research
    setState(prev => ({
      ...prev,
      isOpen: true,
      items: filteredItems,
      categoryGroups,
      hierarchicalStructure,
      selectedItems: new Set(), // Start with nothing selected
      selectedCategories: new Set(),
      processedCount: 0,
      totalCount: 0, // No items selected initially
    }))
  }, [
    frontendTreeStructure,
    findAllQuestions,
    groupQuestionsByCategory,
    createHierarchicalStructure,
  ])

  /**
   * Close batch research dialog and reset state
   */
  const closeBatchResearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      isOpen: false,
      items: [],
      categoryGroups: [],
      hierarchicalStructure: [],
      selectedItems: new Set(),
      selectedCategories: new Set(),
      isProcessing: false,
      processedCount: 0,
      totalCount: 0,
      currentProcessingId: null,
      // Reset coach mode state
      isCoachMode: false,
      currentQuestionIndex: 0,
    }))
  }, [])

  /**
   * Toggle selection of a research item
   */
  const toggleItemSelection = useCallback(
    (nodeId: string) => {
      if (!dragTreeId) return

      const currentSelections = getSelections(dragTreeId)
      const willSelect = !currentSelections.has(nodeId)

      // Get current coach mode state
      const currentCoachMode = isCoachModeOpen(dragTreeId)

      // Check if tier allows any selections at all
      if (willSelect && tierBatchLimit === 0) {
        const tierName =
          tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase()
        toast.error(`${tierName} tier cannot select items for batch research.`)
        return
      }

      // Only enforce limit in list mode, not in coach mode
      if (
        willSelect &&
        !currentCoachMode &&
        currentSelections.size >= Math.min(MAX_BATCH_ITEMS, tierBatchLimit)
      ) {
        const effectiveLimit = Math.min(MAX_BATCH_ITEMS, tierBatchLimit)
        const tierName =
          tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase()

        toast.error(
          `${tierName} tier can select up to ${effectiveLimit} questions for batch research. ${
            tier === SubscriptionTier.FREE
              ? 'Upgrade to PRO to unlock more.'
              : ''
          }`
        )
        return
      }

      // Update persistent store only - let the store be the single source of truth
      toggleSelection(dragTreeId, nodeId)

      // Get updated selections after store update
      const updatedSelections = getSelections(dragTreeId)

      // Sync local state with store state
      setState(prev => ({
        ...prev,
        selectedItems: updatedSelections,
      }))
    },
    [dragTreeId, getSelections, toggleSelection, isCoachModeOpen]
  )

  /**
   * Toggle selection of all items in a category
   */
  const toggleCategorySelection = useCallback((categoryId: string) => {
    let exceeded = false
    setState(prev => {
      const categoryGroup = prev.categoryGroups.find(
        g => g.categoryId === categoryId
      )
      if (!categoryGroup) return prev

      const researchableItems = categoryGroup.items.filter(
        i => !i.hasExistingResearch
      )
      const newSelected = new Set(prev.selectedItems)

      const allSelected = researchableItems.every(i =>
        newSelected.has(i.nodeId)
      )

      if (allSelected) {
        researchableItems.forEach(i => newSelected.delete(i.nodeId))
      } else {
        // Only enforce limit in list mode, not in coach mode
        if (!prev.isCoachMode) {
          const remaining =
            Math.min(MAX_BATCH_ITEMS, tierBatchLimit) - newSelected.size
          const itemsToAdd = researchableItems.filter(
            i => !newSelected.has(i.nodeId)
          )
          if (itemsToAdd.length > remaining) {
            exceeded = true
            return prev
          }
          itemsToAdd.forEach(i => newSelected.add(i.nodeId))
        } else {
          // In coach mode, add all items without limit
          const itemsToAdd = researchableItems.filter(
            i => !newSelected.has(i.nodeId)
          )
          itemsToAdd.forEach(i => newSelected.add(i.nodeId))
        }
      }

      return { ...prev, selectedItems: newSelected }
    })

    if (exceeded) {
      const effectiveLimit = Math.min(MAX_BATCH_ITEMS, tierBatchLimit)
      const tierName =
        tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase()
      toast.error(
        `Selection limit reached. ${tierName} tier allows up to ${effectiveLimit} selections. Deselect some items first.`
      )
    }
  }, [])

  /**
   * Toggle coach mode on/off - FEATURE TEMPORARILY DISABLED
   * This function implements the complete coach mode logic including carousel
   * navigation, question filtering, and state management. Currently disabled
   * via UI button removal but fully functional for potential future use.
   */
  const toggleCoachMode = useCallback(() => {
    if (!dragTreeId) return

    // Determine new coach mode state from current hook state
    const newCoachModeState = !state.isCoachMode

    // Persist to store first (outside of setState to avoid nested updates)
    setCoachModeOpen(dragTreeId, newCoachModeState)

    // Determine the first (or cleared) current question _before_ local state update
    let firstQuestionId: string | null = null
    if (newCoachModeState) {
      const coachModeQuestions = state.items.filter(
        item =>
          !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
      )
      firstQuestionId = coachModeQuestions[0]?.nodeId ?? null
    }

    // Update current question tracking in the store (also outside setState)
    setCurrentQuestionId(dragTreeId, firstQuestionId)

    // compute total questions available for coach mode when starting
    let coachTotal = state.coachModeTotal
    if (newCoachModeState) {
      const coachModeQuestions = state.items.filter(
        item =>
          !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
      )
      coachTotal = coachModeQuestions.length
      firstQuestionId = coachModeQuestions[0]?.nodeId ?? null
    }

    setCurrentQuestionId(dragTreeId, firstQuestionId)

    // Finally, update local state (pure) – no other external calls inside
    setState(prev => ({
      ...prev,
      isCoachMode: newCoachModeState,
      currentQuestionIndex: 0,
      coachModeTotal: coachTotal,
    }))
  }, [
    dragTreeId,
    state.isCoachMode,
    state.items,
    state.selectedItems,
    setCoachModeOpen,
    setCurrentQuestionId,
  ])

  /**
   * Navigate to next question in coach mode
   */
  const goToNextQuestion = useCallback(() => {
    if (!dragTreeId || !state.isCoachMode) return

    const coachModeQuestions = state.items.filter(
      item => !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
    )

    if (coachModeQuestions.length === 0) return

    const maxIndex = coachModeQuestions.length - 1
    const nextIndex = Math.min(state.currentQuestionIndex + 1, maxIndex)
    const nextQuestionId = coachModeQuestions[nextIndex]?.nodeId ?? null

    // Update store first
    setCurrentQuestionId(dragTreeId, nextQuestionId)

    // Then update local state
    setState(prev => ({ ...prev, currentQuestionIndex: nextIndex }))
  }, [dragTreeId, state])

  /**
   * Navigate to previous question in coach mode
   */
  const goToPreviousQuestion = useCallback(() => {
    if (!dragTreeId || !state.isCoachMode) return

    const coachModeQuestions = state.items.filter(
      item => !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
    )

    if (coachModeQuestions.length === 0) return

    const prevIndex = Math.max(state.currentQuestionIndex - 1, 0)
    const prevQuestionId = coachModeQuestions[prevIndex]?.nodeId ?? null

    setCurrentQuestionId(dragTreeId, prevQuestionId)
    setState(prev => ({ ...prev, currentQuestionIndex: prevIndex }))
  }, [dragTreeId, state])

  /**
   * Select current question and move to next in coach mode
   */
  const selectCurrentQuestion = useCallback(() => {
    if (!dragTreeId || !state.isCoachMode) return

    const coachModeQuestions = state.items.filter(
      item => !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
    )

    if (coachModeQuestions.length === 0) return

    const currentItem = coachModeQuestions[state.currentQuestionIndex]
    if (!currentItem) return

    // Persist selection to store first
    toggleSelection(dragTreeId, currentItem.nodeId)

    const updatedSelections = new Set(getSelections(dragTreeId))

    // Prepare next question index/id
    const remainingQuestions = coachModeQuestions.filter(
      item => item.nodeId !== currentItem.nodeId
    )
    const nextIndex = Math.min(
      state.currentQuestionIndex,
      remainingQuestions.length - 1
    )
    const nextQuestionId = remainingQuestions[nextIndex]?.nodeId ?? null

    // Update store with next question id
    setCurrentQuestionId(dragTreeId, nextQuestionId)

    // Sync local state
    setState(prev => ({
      ...prev,
      selectedItems: updatedSelections,
      currentQuestionIndex: Math.max(0, nextIndex),
    }))
  }, [dragTreeId, state, toggleSelection, getSelections, setCurrentQuestionId])

  /**
   * Skip current question and move to next in coach mode
   */
  const skipCurrentQuestion = useCallback(() => {
    if (!dragTreeId || !state.isCoachMode) return

    const coachModeQuestions = state.items.filter(
      item => !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
    )

    if (coachModeQuestions.length === 0) return

    const nextIndex = Math.min(
      state.currentQuestionIndex + 1,
      coachModeQuestions.length - 1
    )
    const nextQuestionId = coachModeQuestions[nextIndex]?.nodeId ?? null

    setCurrentQuestionId(dragTreeId, nextQuestionId)
    setState(prev => ({ ...prev, currentQuestionIndex: nextIndex }))
  }, [dragTreeId, state, setCurrentQuestionId])

  /**
   * Get filtered questions for coach mode (exclude already researched and selected questions)
   */
  const getCoachModeQuestions = useCallback(() => {
    if (!state.isCoachMode) return state.items

    // Filter out questions that are already researched or already selected
    return state.items.filter(
      item => !item.hasExistingResearch && !state.selectedItems.has(item.nodeId)
    )
  }, [state.isCoachMode, state.items, state.selectedItems])

  /**
   * Get current question in coach mode
   */
  const getCurrentQuestion = useCallback(() => {
    if (!state.isCoachMode) return null

    const coachModeQuestions = getCoachModeQuestions()
    if (coachModeQuestions.length === 0) return null

    return coachModeQuestions[state.currentQuestionIndex] || null
  }, [state.isCoachMode, state.currentQuestionIndex, getCoachModeQuestions])

  /**
   * Execute research for a single item (same logic as ResearchButton)
   */
  const executeResearchForItem = useCallback(
    async (item: BatchResearchItem): Promise<void> => {
      try {
        // Mark the node as interested when user performs research action
        markNodeAsInterested(item.nodeId)

        // Create the research content record
        const createPayload = {
          dragTreeNodeId: item.nodeId,
          questionText: item.displayText,
          researchType: 'QUICK_RESEARCH',
        }

        const createResponse = await fetch('/api/dragtree/research_create', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(createPayload),
        })

        if (!createResponse.ok) {
          throw new Error('Failed to create research content record')
        }

        const createResult = await createResponse.json()
        const contentId = createResult.data.contentId

        // Add to store to trigger ResearchDisplay
        addNodeContent(item.nodeId, {
          contentId,
          contentType: NodeContentType.QUICK_RESEARCH,
          contentVersion: 'v1',
          status: DragTreeNodeContentStatus.INITIALIZED,
          contentText: '',
          metadata: {
            questionText: item.displayText,
            originalQuestion: item.displayText,
          },
        })

        console.log(
          `✅ [BatchResearch] Research started for: ${item.questionText}`
        )
      } catch (error) {
        console.error(
          `❌ [BatchResearch] Error starting research for ${item.questionText}:`,
          error
        )
        throw error
      }
    },
    [markNodeAsInterested, addNodeContent]
  )

  /**
   * Start batch research process with rate limiting
   */
  const startBatchResearch = useCallback(async () => {
    // Always grab the latest selections from the persistent store
    const latestSelections = dragTreeId
      ? getSelections(dragTreeId)
      : state.selectedItems

    const selectedItems = state.items.filter(item =>
      latestSelections.has(item.nodeId)
    )

    if (selectedItems.length === 0) {
      toast.error('No items selected for research')
      return
    }

    const effectiveLimit = Math.min(MAX_BATCH_ITEMS, tierBatchLimit)
    if (selectedItems.length > effectiveLimit) {
      const tierName =
        tier.charAt(0).toUpperCase() + tier.slice(1).toLowerCase()
      toast.error(
        `${tierName} tier can research up to ${effectiveLimit} questions at once. Please deselect some items and try again.`
      )
      return
    }

    // Prevent exceeding per-tree quick research total limit (client-side precheck)
    try {
      if (quickPerTreeLimit > 0) {
        // Fetch current node content count quickly via a lightweight endpoint or store
        // We approximate via store counts to avoid heavy network: count all content items in current tree
        const allContentMaps = useDragTreeStore.getState().nodeContent
        let currentCount = 0
        allContentMaps.forEach(map => {
          map.forEach(item => {
            if (item.contentType === NodeContentType.QUICK_RESEARCH)
              currentCount++
          })
        })

        if (currentCount + selectedItems.length > quickPerTreeLimit) {
          const remaining = Math.max(0, quickPerTreeLimit - currentCount)
          toast.error(
            `This selection would exceed your per-tree quick research limit (${quickPerTreeLimit}). You can add up to ${remaining} more.`
          )
          return
        }
      }
    } catch (e) {
      // Non-fatal; let server enforce as source of truth
    }

    // Log the batch research event
    logEventWithContext(
      'click_dragtree_batchResearch',
      session?.user?.id,
      dragTreeId,
      {
        question_ids: selectedItems.map(item => item.nodeId),
        question_texts: selectedItems.map(item =>
          item.questionText.slice(0, 100)
        ), // Truncate for logging
        batch_count: selectedItems.length,
        tier: tier,
        effective_limit: effectiveLimit,
      }
    )

    // Capture the ID of the first selected item before state mutations
    const firstSelectedId = selectedItems[0].nodeId

    setState(prev => ({
      ...prev,
      isProcessing: true,
      processedCount: 0,
      totalCount: selectedItems.length,
    }))

    console.log(
      `🚀 [BatchResearch] Starting batch research for ${selectedItems.length} items`
    )

    let processedCount = 0

    for (const item of selectedItems) {
      try {
        // Update current processing item
        setState(prev => ({
          ...prev,
          currentProcessingId: item.nodeId,
          items: prev.items.map(i =>
            i.nodeId === item.nodeId
              ? { ...i, status: 'processing' as const }
              : i
          ),
        }))

        // Execute research for this item
        await executeResearchForItem(item)

        processedCount++

        // Update item status to completed
        setState(prev => ({
          ...prev,
          processedCount,
          items: prev.items.map(i =>
            i.nodeId === item.nodeId
              ? { ...i, status: 'completed' as const }
              : i
          ),
        }))

        // Rate limiting: simulate rapid manual clicking (~1 second per item)
        if (processedCount < selectedItems.length) {
          // Simulate rapid manual clicking: ~1 second per item with slight randomness
          const baseDelay = 1000 // 1 second base
          const randomVariation = 300 // ±300ms for natural feel

          const minDelay = baseDelay - randomVariation // 700ms
          const maxDelay = baseDelay + randomVariation // 1300ms
          const delay = Math.random() * (maxDelay - minDelay) + minDelay

          console.log(
            `⏳ [BatchResearch] Waiting ${Math.round(delay)}ms before next request`
          )
          await new Promise(resolve => setTimeout(resolve, delay))
        }
      } catch (error) {
        console.error(
          `❌ [BatchResearch] Failed to process ${item.questionText}:`,
          error
        )

        // Update item status to failed
        setState(prev => ({
          ...prev,
          items: prev.items.map(i =>
            i.nodeId === item.nodeId
              ? {
                  ...i,
                  status: 'failed' as const,
                  error:
                    error instanceof Error ? error.message : 'Unknown error',
                }
              : i
          ),
        }))
      }
    }

    // Mark processing complete but keep dialog open for UX grace period
    setState(prev => ({
      ...prev,
      processedCount,
      currentProcessingId: null,
    }))

    // Clear processed selections from persistent store
    if (dragTreeId) {
      const processedNodeIds = selectedItems.map(item => item.nodeId)
      const currentSelections = getSelections(dragTreeId)
      const remainingSelections = new Set(
        Array.from(currentSelections).filter(
          id => !processedNodeIds.includes(id)
        )
      )
      setSelections(dragTreeId, remainingSelections)
    }

    // Grace delay (3 s) then close & navigate
    setTimeout(() => {
      closeBatchResearch()
      navigateToTreeNode(firstSelectedId)
    }, 3000) // 3s delay for UX grace period

    toast.success(`All ${selectedItems.length} research requests queued!`)

    console.log(
      `🎉 [BatchResearch] All ${selectedItems.length} research requests have been queued successfully.`
    )
  }, [
    state.items,
    state.selectedItems,
    executeResearchForItem,
    closeBatchResearch,
    navigateToTreeNode,
    dragTreeId,
    getSelections,
    setSelections,
  ])

  /**
   * Cancel ongoing batch research
   */
  const cancelBatchResearch = useCallback(() => {
    setState(prev => ({
      ...prev,
      isProcessing: false,
      currentProcessingId: null,
    }))

    toast.success('Batch research cancelled')
    console.log('🛑 [BatchResearch] Batch process cancelled by user')
  }, [])

  return {
    // State
    isOpen: state.isOpen,
    items: state.items,
    categoryGroups: state.categoryGroups,
    hierarchicalStructure: state.hierarchicalStructure,
    selectedItems: state.selectedItems,
    selectedCategories: state.selectedCategories,
    isProcessing: state.isProcessing,
    processedCount: state.processedCount,
    totalCount: state.totalCount,
    currentProcessingId: state.currentProcessingId,

    // Coach mode state
    isCoachMode: state.isCoachMode,
    currentQuestionIndex: state.currentQuestionIndex,
    currentQuestion: getCurrentQuestion(),
    coachModeQuestions: getCoachModeQuestions(),
    coachModeTotal: state.coachModeTotal,

    // Actions
    openBatchResearch,
    closeBatchResearch,
    toggleItemSelection,
    toggleCategorySelection,
    startBatchResearch,
    cancelBatchResearch,

    // Coach mode actions
    toggleCoachMode,
    goToNextQuestion,
    goToPreviousQuestion,
    selectCurrentQuestion,
    skipCurrentQuestion,
  }
}
