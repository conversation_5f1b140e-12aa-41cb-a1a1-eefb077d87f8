'use client'

import React from 'react'
import { useSession } from 'next-auth/react'
import { useDragTreeStore } from '@/app/stores/dragtree_store'
import { logEventWithContext } from '@/app/libs/logging'
import { Button } from '@/components/ui/button'
import { Eye, EyeOff, ExternalLink } from 'lucide-react'
import { FiList } from 'react-icons/fi'
import { cn } from '@/lib/utils'
import { Dialog, DialogContent, DialogTrigger } from '@/components/ui/dialog'
import QuickResearchStepsDialogBody from '@/app/(conv)/dragTree/[dragTreeId]/components/shared/QuickResearchStepsDialogBody'
import { DragTreeNodeContentStatus } from '@prisma/client'

export type QuickResearchHeaderSectionProps = {
  status: DragTreeNodeContentStatus | null
  isPersistent: boolean
  isStreaming: boolean
  isStuckProcessing: boolean
  stepMessages?: any[] | null
  localContent: string
  onToggleVisibility: () => void
  onOpenInTab: () => void
  onOpenSteps?: () => Promise<void> | void
}

export const QuickResearchHeaderSection: React.FC<
  QuickResearchHeaderSectionProps
> = ({
  status,
  isPersistent,
  isStreaming,
  isStuckProcessing,
  stepMessages,
  localContent,
  onToggleVisibility,
  onOpenInTab,
  onOpenSteps,
}) => {
  const [stepsModalOpen, setStepsModalOpen] = React.useState<boolean>(false)
  const { data: session } = useSession()
  const dragTreeId = useDragTreeStore(state => state.dragTreeId)

  return (
    <div className="flex items-center justify-between">
      {/* Left: Quick Research button and steps/sources */}
      <div className="flex items-center gap-2 flex-1 min-w-0">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleVisibility}
          className={cn(
            'h-6 px-2 text-xs font-normal transition-all duration-200',
            'bg-slate-50 hover:bg-slate-100 border border-slate-200',
            'text-slate-600 hover:text-slate-700',
            {
              'bg-blue-50 border-blue-200 text-blue-700': isPersistent,
            }
          )}
        >
          {isPersistent ? (
            <EyeOff className="h-3 w-3 mr-1" />
          ) : (
            <Eye className="h-3 w-3 mr-1" />
          )}
          <span className="text-xs">
            {isStuckProcessing
              ? 'Retry Research'
              : isStreaming || status === DragTreeNodeContentStatus.PROCESSING
                ? 'Researching...'
                : 'Quick Research'}
          </span>
          {status === DragTreeNodeContentStatus.ACTIVE && !isStreaming && (
            <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-green-500"></span>
          )}
          {isStuckProcessing && (
            <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-red-500 animate-pulse"></span>
          )}
          {(status === DragTreeNodeContentStatus.PROCESSING || isStreaming) &&
            !isStuckProcessing && (
              <span className="ml-1.5 w-1.5 h-1.5 rounded-full bg-amber-500 animate-pulse"></span>
            )}
        </Button>

        {/* Steps Button */}
        {status === DragTreeNodeContentStatus.ACTIVE && (
          <Dialog
            open={stepsModalOpen}
            onOpenChange={async (open: boolean) => {
              if (open && typeof onOpenSteps === 'function') {
                try {
                  await onOpenSteps()
                } catch (e) {
                  console.warn('Failed to prefetch steps on open:', e)
                }
              }
              // Log steps dialog open/close transitions; only report open events
              if (open) {
                const stepsCount = Array.isArray(stepMessages)
                  ? stepMessages.length
                  : 0
                logEventWithContext(
                  'click_tab_quickResearchReasoningSteps',
                  session?.user?.id,
                  dragTreeId || undefined,
                  {
                    node_id:
                      (typeof window !== 'undefined' &&
                        (document.activeElement?.getAttribute?.(
                          'data-node-id'
                        ) ||
                          '')) ||
                      '',
                    content_id: null,
                    steps_count: stepsCount,
                  }
                )
              }
              setStepsModalOpen(open)
            }}
          >
            <DialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="h-6 px-2 text-xs font-normal border-0 bg-gray-50/60 text-gray-600 hover:bg-gray-100/80 hover:text-gray-800"
              >
                <FiList className="h-3 w-3 mr-1" /> Steps
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[80vh] p-0">
              <QuickResearchStepsDialogBody messages={stepMessages || []} />
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Right: Open in Tab */}
      {status === DragTreeNodeContentStatus.ACTIVE && localContent.trim() && (
        <Button
          variant="ghost"
          size="sm"
          onClick={onOpenInTab}
          className={cn(
            'h-6 px-2 text-xs font-normal border-0',
            'bg-gray-50/60 text-gray-500 hover:bg-gray-100/80 hover:text-gray-700'
          )}
          title="Open in separate tab"
        >
          <ExternalLink className="h-3 w-3" />
        </Button>
      )}
    </div>
  )
}
