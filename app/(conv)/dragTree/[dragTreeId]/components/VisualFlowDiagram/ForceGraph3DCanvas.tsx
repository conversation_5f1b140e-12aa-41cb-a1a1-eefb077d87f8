'use client'

import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
  Suspense,
  lazy,
} from 'react'
import { LayoutModeToggle } from './LayoutModeToggle'
import { useForceGraphData } from '@/app/(conv)/dragTree/[dragTreeId]/hooks/useForceGraphData'
import { useNavigationStore } from '@/app/stores/navigation_store'
import { cn } from '@/lib/utils'
import * as THREE from 'three'
// @ts-ignore - types are bundled with the lib
import SpriteText from 'three-spritetext'
import { Button } from '@/components/ui/button'
import type { NodeObject, LinkObject, ForceGraphMethods } from 'react-force-graph-3d'

const ForceGraph3D = lazy(() =>
  import('react-force-graph-3d').then(m => ({ default: m.default }))
)
type SpriteTextInstance = InstanceType<typeof SpriteText>
type ForceGraphNode = NodeObject & {
  id: string | number
  name?: string
  type?: 'category' | string
  level?: number
  deg?: number
  x?: number
  y?: number
  z?: number
  __threeObj?: (THREE.Group & { __label?: SpriteTextInstance | null }) | null
} & Record<string, unknown>
type ForceGraphLink = LinkObject & {
  source: ForceGraphNode | string | number
  target: ForceGraphNode | string | number
} & Record<string, unknown>

const clamp = (value: number, min: number, max: number) =>
  Math.min(max, Math.max(min, value))

const DEFAULT_VIEW_VECTOR = new THREE.Vector3(1, 0.42, 1).normalize()

const getSafeCoordinate = (value: unknown): number =>
  typeof value === 'number' && Number.isFinite(value) ? value : 0

type OrbitControlsLike = {
  target?: THREE.Vector3 | { x?: number; y?: number; z?: number }
}

const resolveNodeId = (
  value: ForceGraphNode | string | number | null | undefined
): string | null => {
  if (value === null || value === undefined) return null
  if (typeof value === 'object') {
    const nodeId = (value as ForceGraphNode).id
    if (nodeId === undefined || nodeId === null) return null
    return String(nodeId)
  }
  return String(value)
}
type Props = {
  layoutMode: 'force3d' | 'force' | 'linear' | 'radial'
  setLayoutMode: (m: 'force3d' | 'force' | 'linear' | 'radial') => void
  dragTreeId: string
}

const ForceGraph3DCanvas: React.FC<Props> = ({ layoutMode, setLayoutMode }) => {
  const graphData = useForceGraphData()
  const nodes = graphData.nodes as ForceGraphNode[]
  const links = graphData.links as ForceGraphLink[]
  const fgRef = useRef<ForceGraphMethods | undefined>(undefined)
  const [size, setSize] = useState<{ width: number; height: number }>({
    width: 800,
    height: 600,
  })
  const containerRef = useRef<HTMLDivElement | null>(null)
  const targetNodeId = useNavigationStore(s => s.targetNodeId)
  const [selectedId, setSelectedId] = useState<string | null>(null)
  const [hoverFocusIds, setHoverFocusIds] = useState<Set<string>>(new Set())
  const [selectionFocusIds, setSelectionFocusIds] = useState<Set<string>>(new Set())
  const didFitRef = useRef(false)
  const initialViewRef = useRef<{ position: THREE.Vector3; target: THREE.Vector3 } | null>(
    null
  )

  useEffect(() => {
    if (!containerRef.current) return
    const el = containerRef.current
    const ro = new ResizeObserver(entries => {
      for (const entry of entries) {
        const cr = entry.contentRect
        setSize({
          width: Math.max(200, cr.width),
          height: Math.max(200, cr.height),
        })
      }
    })
    ro.observe(el)
    return () => ro.disconnect()
  }, [])

  const data = useMemo(() => ({ nodes, links }), [nodes, links])

  const nodeLookup = useMemo(() => {
    const map = new Map<string, ForceGraphNode>()
    nodes.forEach(node => {
      map.set(String(node.id), node)
    })
    return map
  }, [nodes])

  const { parentMap, childrenMap } = useMemo(() => {
    const parents = new Map<string, string>()
    const children = new Map<string, Set<string>>()

    links.forEach(link => {
      const sourceId = resolveNodeId(link.source)
      const targetId = resolveNodeId(link.target)
      if (!sourceId || !targetId) return
      parents.set(targetId, sourceId)
      const bucket = children.get(sourceId)
      if (bucket) {
        bucket.add(targetId)
      } else {
        children.set(sourceId, new Set([targetId]))
      }
    })

    return { parentMap: parents, childrenMap: children }
  }, [links])

  const getGraphRadius = useCallback((): number => {
    if (!nodes.length) return 480
    let maxDistance = 0
    let totalDistance = 0
    let counted = 0
    nodes.forEach(node => {
      const x = getSafeCoordinate(node.x)
      const y = getSafeCoordinate(node.y)
      const z = getSafeCoordinate(node.z)
      const distance = Math.sqrt(x * x + y * y + z * z)
      if (Number.isFinite(distance)) {
        maxDistance = Math.max(maxDistance, distance)
        totalDistance += distance
        counted += 1
      }
    })
    if (!counted) return 480
    const averageDistance = totalDistance / counted
    const baseline = Math.max(maxDistance, averageDistance * 1.35)
    return clamp(baseline || 480, 360, 1800)
  }, [nodes])

  const computeFamilySet = useCallback(
    (nodeId: string) => {
      const family = new Set<string>([nodeId])

      let current: string | undefined = nodeId
      const visitedAncestors = new Set<string>()
      while (current) {
        if (visitedAncestors.has(current)) break
        visitedAncestors.add(current)
        const parent = parentMap.get(current)
        if (!parent) break
        family.add(parent)
        current = parent
      }

      const stack = [nodeId]
      const visitedDesc = new Set<string>()
      while (stack.length > 0) {
        const nextId = stack.pop() as string
        if (visitedDesc.has(nextId)) continue
        visitedDesc.add(nextId)
        const childSet = childrenMap.get(nextId)
        if (!childSet) continue
        childSet.forEach(childId => {
          if (!family.has(childId)) family.add(childId)
          stack.push(childId)
        })
      }

      return family
    },
    [childrenMap, parentMap]
  )

  // Custom 3D object: sphere + transparent label
  const nodeThreeObject = useCallback(
    (rawNode: NodeObject & Record<string, unknown>) => {
      const node = rawNode as ForceGraphNode
      if (node.id === undefined || node.id === null) {
        return new THREE.Group()
      }
      const group = new THREE.Group()
      const isRoot = node.level === 1
      const isCategory = node.type === 'category'
      const baseSphereColor = isRoot
        ? 0xf59e0b
      : isCategory
        ? 0x2563eb
        : 0x10b981

    const sphereGeometry = new THREE.SphereGeometry(
      isRoot ? 10 : 4 + (node.level || 1),
      16,
      16
    )
    const sphereMaterial = new THREE.MeshStandardMaterial({
      color: baseSphereColor,
      roughness: 0.5,
      metalness: 0.12,
      emissive: isRoot
        ? new THREE.Color(0xf59e0b)
        : new THREE.Color(0x111827),
      emissiveIntensity: isRoot ? 0.28 : 0.06,
    })
    const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial)
    group.add(sphere)

    const labelText = String(node.name || node.id)
    const label = new (SpriteText as any)(labelText, 8) as SpriteTextInstance
    const baseLabelSize = isRoot ? 12 : isCategory ? 10 : 6
    const baseLabelOpacity = isCategory ? 0.95 : isRoot ? 1.0 : 0.55
    const labelColor = isRoot ? '#0f172a' : isCategory ? '#1d4ed8' : '#0f172a'

    label.color = labelColor
    label.textHeight = baseLabelSize
    label.material.transparent = true
    label.material.opacity = baseLabelOpacity
    label.material.depthWrite = false
    label.center.set(0.5, 0)
    label.position.set(0, isRoot ? 18 : 10, 0)
    // Prevent label sprite from capturing drag/raycast to reduce DragControls glitches
    label.raycast = () => null

    group.userData = {
      nodeId: String(node.id),
      baseLabel: {
        opacity: baseLabelOpacity,
        size: baseLabelSize,
        color: labelColor,
      },
      baseSphere: {
        emissiveIntensity: sphereMaterial.emissiveIntensity,
        scale: 1,
        color: sphereMaterial.color.clone(),
      },
      sphere,
    }
    ;(group as any).__label = label
    group.add(label)
    ;(node as ForceGraphNode).__threeObj = group as any
    return group
    },
    []
  )

  // Centralized label styling so hover/selection/restore never recreate Three objects
  const updateLabelAppearance = useCallback(
    (
      targetNode: ForceGraphNode | undefined,
      overrides?: { opacity?: number; textHeight?: number; color?: string }
    ) => {
      const objectRef = targetNode?.__threeObj as
        | (THREE.Group & { __label?: SpriteTextInstance | null })
        | undefined
      const label = objectRef?.__label
      const base = objectRef?.userData?.baseLabel as
        | { opacity: number; size: number; color: string }
        | undefined
      if (!objectRef || !label || !base) return

      const nextOpacity = overrides?.opacity ?? base.opacity
      const nextSize = overrides?.textHeight ?? base.size
      const nextColor = overrides?.color ?? base.color
      if (typeof nextOpacity === 'number') {
        label.material.opacity = nextOpacity
      }
      if (typeof nextSize === 'number') {
        label.textHeight = nextSize
      }
      if (typeof nextColor === 'string') {
        label.color = nextColor
      }
    },
    []
  )

  const updateSphereAppearance = useCallback(
    (
      targetNode: ForceGraphNode | undefined,
      overrides?: {
        emissiveIntensity?: number
        scale?: number
        color?: THREE.ColorRepresentation
      }
    ) => {
      const objectRef = targetNode?.__threeObj as
        | (THREE.Group & { __label?: SpriteTextInstance | null })
        | undefined
      const sphere: THREE.Mesh<THREE.SphereGeometry, THREE.MeshStandardMaterial> | undefined =
        objectRef?.userData?.sphere
      const base = objectRef?.userData?.baseSphere as
        | { emissiveIntensity: number; scale: number; color: THREE.Color }
        | undefined
      if (!sphere || !base) return

      const material = sphere.material
      if (material && 'emissiveIntensity' in material) {
        const nextIntensity = overrides?.emissiveIntensity ?? base.emissiveIntensity
        material.emissiveIntensity = nextIntensity
      }

      const nextScale = overrides?.scale ?? base.scale ?? 1
      sphere.scale.setScalar(nextScale)

      if (overrides?.color && sphere.material?.color) {
        sphere.material.color.set(overrides.color)
      } else if (sphere.material?.color && base.color) {
        sphere.material.color.copy(base.color)
      }
    },
    []
  )

  const applyNodeAppearance = useCallback(
    (
      node: ForceGraphNode,
      mode: 'base' | 'selection' | 'hover',
      options?: { dim?: boolean }
    ) => {
      const objectRef = node.__threeObj as
        | (THREE.Group & { __label?: SpriteTextInstance | null })
        | undefined
      const baseLabel = objectRef?.userData?.baseLabel as
        | { opacity: number; size: number; color: string }
        | undefined
      if (!objectRef || !baseLabel) return

      const isCategory = node.type === 'category'
      const baseSize = baseLabel.size
      const baseOpacity = baseLabel.opacity
      const baseColor = baseLabel.color

      if (mode === 'hover') {
        const sizeBoost = isCategory ? 4.5 : 2.6
        const labelColor = isCategory ? '#0ea5e9' : '#0f172a'
        const sphereColor = isCategory ? '#38bdf8' : '#34d399'
        updateLabelAppearance(node, {
          opacity: 1,
          textHeight: baseSize + sizeBoost,
          color: labelColor,
        })
        updateSphereAppearance(node, {
          emissiveIntensity: (objectRef.userData.baseSphere?.emissiveIntensity ?? 0.06) + 0.3,
          scale: 1.32,
          color: sphereColor,
        })
        return
      }

      if (mode === 'selection') {
        const sizeBoost = isCategory ? 3.5 : 2.2
        const labelColor = isCategory ? '#2563eb' : baseColor
        const sphereColor = isCategory ? '#3b82f6' : '#14b8a6'
        updateLabelAppearance(node, {
          opacity: 1,
          textHeight: baseSize + sizeBoost,
          color: labelColor,
        })
        updateSphereAppearance(node, {
          emissiveIntensity: (objectRef.userData.baseSphere?.emissiveIntensity ?? 0.06) + 0.18,
          scale: 1.18,
          color: sphereColor,
        })
        return
      }

      const dimmed = options?.dim ?? false
      const dimOpacity = Math.max(0.22, baseOpacity * 0.35)
      const labelOpacity = dimmed ? dimOpacity : baseOpacity
      const labelSize = dimmed ? baseSize * 0.95 : baseSize
      const labelColor = dimmed ? '#94a3b8' : baseColor
      updateLabelAppearance(node, {
        opacity: labelOpacity,
        textHeight: labelSize,
        color: labelColor,
      })
      const baseSphere = objectRef.userData.baseSphere as
        | { emissiveIntensity: number; scale: number; color: THREE.Color }
        | undefined
      const baseIntensity = baseSphere?.emissiveIntensity ?? 0.06
      updateSphereAppearance(node, {
        emissiveIntensity: dimmed ? Math.max(0, baseIntensity - 0.04) : baseIntensity,
        scale: dimmed ? 0.92 : 1,
      })
    },
    [updateLabelAppearance, updateSphereAppearance]
  )

  const getCurrentViewState = useCallback(() => {
    const graphInstance = fgRef.current
    if (!graphInstance) return null
    const cameraFn = graphInstance.camera?.bind(graphInstance)
    const controlsFn = graphInstance.controls?.bind(graphInstance)
    const camera = cameraFn ? cameraFn() : null
    if (!camera) return null
    const cameraPosition = (camera.position as THREE.Vector3).clone()
    const controls = controlsFn ? (controlsFn() as OrbitControlsLike | null) : null
    const targetSource = controls?.target
    const targetVector = targetSource
      ? targetSource instanceof THREE.Vector3
        ? targetSource.clone()
        : new THREE.Vector3(
            getSafeCoordinate((targetSource as any).x),
            getSafeCoordinate((targetSource as any).y),
            getSafeCoordinate((targetSource as any).z)
          )
      : new THREE.Vector3()
    return { position: cameraPosition, target: targetVector }
  }, [])

  const moveCameraTo = useCallback(
    (position: THREE.Vector3, target: THREE.Vector3, duration = 820) => {
      if (!fgRef.current) return
      fgRef.current.cameraPosition(
        { x: position.x, y: position.y, z: position.z },
        { x: target.x, y: target.y, z: target.z },
        duration
      )
    },
    []
  )

  // Shared camera easing helper so clicks and outline navigation feel consistent
  const focusNode = useCallback(
    (
      node: ForceGraphNode,
      options?: { duration?: number; distanceMultiplier?: number }
    ) => {
      if (!fgRef.current || !node) return
      const radius = getGraphRadius()
      const viewState = getCurrentViewState()
      const lookAtVector = viewState?.target
        ? viewState.target.clone()
        : new THREE.Vector3()
      let viewDirection = viewState?.position
        ? viewState.position.clone().sub(lookAtVector)
        : DEFAULT_VIEW_VECTOR.clone().multiplyScalar(radius)
      if (viewDirection.lengthSq() === 0) viewDirection = DEFAULT_VIEW_VECTOR.clone()
      viewDirection.normalize()

      const neighborBonus = typeof node.deg === 'number' ? node.deg * 18 : 0
      const baseDistance = radius * (options?.distanceMultiplier ?? 0.7)
      const distance = clamp(baseDistance + neighborBonus, 320, radius * 1.35)

      const targetVector = new THREE.Vector3(
        getSafeCoordinate(node.x),
        getSafeCoordinate(node.y),
        getSafeCoordinate(node.z)
      )
      const newCameraPosition = targetVector
        .clone()
        .add(viewDirection.clone().multiplyScalar(distance))

      moveCameraTo(newCameraPosition, targetVector, options?.duration ?? 820)
    },
    [getCurrentViewState, getGraphRadius, moveCameraTo]
  )

  // Bring camera back to first-fit baseline without disturbing node selection
  const handleResetView = useCallback(() => {
    if (!fgRef.current) return
    const initial = initialViewRef.current
    if (initial) {
      moveCameraTo(initial.position, initial.target, 650)
    } else {
      try {
        fgRef.current.zoomToFit(700, 140)
      } catch {}
    }
    setHoverFocusIds(new Set())
    nodes.forEach(node => {
      applyNodeAppearance(node, 'base')
    })
  }, [applyNodeAppearance, moveCameraTo, nodes])

  const handleZoomStep = useCallback(
    (direction: 'in' | 'out') => {
      const viewState = getCurrentViewState()
      if (!viewState) return
      const { position, target } = viewState
      const directionVector = position.clone().sub(target)
      const currentDistance = directionVector.length()
      if (currentDistance === 0) return
      const multiplier = direction === 'in' ? 0.78 : 1.26
      const newDistance = clamp(currentDistance * multiplier, 120, 4000)
      const newPosition = target
        .clone()
        .add(directionVector.normalize().multiplyScalar(newDistance))
      moveCameraTo(newPosition, target, 480)
    },
    [getCurrentViewState, moveCameraTo]
  )

  // Clicking a node should scroll outline to it and ease camera focus
  const handleNodeClick = useCallback(
    (rawNode: (NodeObject & Record<string, unknown>) | null) => {
      const node = rawNode as ForceGraphNode | null
      if (!node?.id) return
      const nodeId = String(node.id)
      try {
        const nav = (useNavigationStore.getState() as any).navigateToTreeNode
        if (typeof nav === 'function') nav(nodeId)
      } catch {}
      setSelectedId(nodeId)
      setSelectionFocusIds(computeFamilySet(nodeId))
      focusNode(node, { duration: 780, distanceMultiplier: 0.68 })
    },
    [computeFamilySet, focusNode]
  )

  // Hover handler: emphasize label
  const handleNodeHover = useCallback(
    (rawNode: (NodeObject & Record<string, unknown>) | null) => {
      const node = rawNode as ForceGraphNode | null
      if (node?.id) {
        setHoverFocusIds(computeFamilySet(String(node.id)))
      } else {
        setHoverFocusIds(new Set())
      }
    },
    [computeFamilySet]
  )

  useEffect(() => {
    if (selectedId) {
      setSelectionFocusIds(computeFamilySet(selectedId))
    } else {
      setSelectionFocusIds(new Set())
    }
  }, [computeFamilySet, selectedId])

  useEffect(() => {
    const hasActiveFocus = hoverFocusIds.size > 0 || selectionFocusIds.size > 0
    nodes.forEach(node => {
      if (!node?.id) return
      const nodeId = String(node.id)
      const mode: 'base' | 'selection' | 'hover' = hoverFocusIds.has(nodeId)
        ? 'hover'
        : selectionFocusIds.has(nodeId)
          ? 'selection'
          : 'base'
      applyNodeAppearance(node, mode, { dim: hasActiveFocus && mode === 'base' })
    })
  }, [applyNodeAppearance, hoverFocusIds, nodes, selectionFocusIds])

  // Focus/zoom when navigating from outline (zoom out slightly more)
  useEffect(() => {
    if (!targetNodeId) return
    const target = nodeLookup.get(String(targetNodeId))
    if (!target) return
    const timer = window.setTimeout(() => {
      try {
        focusNode(target, { duration: 920, distanceMultiplier: 1.05 })
      } catch {}
    }, 80)
    return () => window.clearTimeout(timer)
  }, [focusNode, nodeLookup, targetNodeId])

  // Tune forces and initial zoom-to-fit
  useEffect(() => {
    if (!fgRef.current) return
    const linkForce: any = fgRef.current.d3Force('link')
    if (linkForce && typeof linkForce.distance === 'function') {
      const distanceResolver = (link: ForceGraphLink) => {
        const resolveNode = (value: ForceGraphNode | string | number): ForceGraphNode | null => {
          if (typeof value === 'object') return value as ForceGraphNode
          return nodeLookup.get(String(value)) ?? null
        }

        const sourceNode = resolveNode(link.source)
        const targetNode = resolveNode(link.target)
        const fallback = 220
        if (!sourceNode || !targetNode) return fallback

        const sourceType = sourceNode.type ?? 'category'
        const targetType = targetNode.type ?? 'category'
        const maxLevel = Math.max(sourceNode.level ?? 1, targetNode.level ?? 1)
        const longestLabel = Math.max(
          sourceNode.name?.length ?? 0,
          targetNode.name?.length ?? 0
        )

        const involvesRoot = (sourceNode.level ?? 1) === 1 || (targetNode.level ?? 1) === 1
        const hasLeaf = sourceType !== 'category' || targetType !== 'category'

        if (hasLeaf) {
          const base = involvesRoot ? 170 : 150
          const depthAdjust = Math.min(45, maxLevel * 6)
          const labelAdjust = Math.min(35, longestLabel * 1.2)
          return clamp(base + depthAdjust + labelAdjust, 120, 230)
        }

        const baseCategorySpacing = involvesRoot ? 320 : 260
        const degreeAdjust = Math.min(
          120,
          ((sourceNode.deg ?? 0) + (targetNode.deg ?? 0)) * 6
        )
        const labelAdjust = Math.min(60, longestLabel * 1.6)
        return clamp(baseCategorySpacing + degreeAdjust + labelAdjust, 240, 420)
      }

      linkForce.distance((link: ForceGraphLink) => distanceResolver(link))
      if (typeof fgRef.current?.d3ReheatSimulation === 'function') {
        fgRef.current.d3ReheatSimulation()
      }
    }
    const charge: any = fgRef.current.d3Force('charge')
    if (charge && typeof charge.strength === 'function') charge.strength(-380)

    // One-time zoom to fit and back off a little to see all content
    if (!didFitRef.current) {
      didFitRef.current = true
      setTimeout(() => {
        try {
          const graph = fgRef.current
          if (!graph) return
          graph.zoomToFit(1000, 120)
          const recordTimer = window.setTimeout(() => {
            const viewState = getCurrentViewState()
            if (viewState) {
              initialViewRef.current = {
                position: viewState.position.clone(),
                target: viewState.target.clone(),
              }
            }
            window.clearTimeout(recordTimer)
          }, 260)
        } catch {}
      }, 200)
    }
  }, [data, getCurrentViewState])

  // Guard against intermittent DragControls null target bug while preserving drag behavior
  useEffect(() => {
    const handler = (e: ErrorEvent) => {
      const msg = e?.message || (e?.error && (e.error as any).message) || ''
      if (
        typeof msg === 'string' &&
        (msg.includes('DragControls') || msg.includes('matrixWorld'))
      ) {
        // Swallow the error to avoid breaking interaction; allow rendering to continue
        e.preventDefault()
        return true
      }
      return false
    }
    window.addEventListener('error', handler)
    return () => window.removeEventListener('error', handler)
  }, [])

  return (
    <div
      ref={containerRef}
      className={cn(
        'h-full w-full relative bg-gradient-to-br from-cyan-50 to-sky-50'
      )}
    >
      <LayoutModeToggle layoutMode={layoutMode} setLayoutMode={setLayoutMode} />
      <Suspense
        fallback={
          <div className="absolute inset-0 flex items-center justify-center text-sm text-gray-500">
            Loading 3D…
          </div>
        }
      >
        <ForceGraph3D
          ref={fgRef}
          width={size.width}
          height={size.height}
          graphData={data as any}
          nodeId="id"
          nodeLabel={(n: any) => n.name}
          nodeThreeObject={nodeThreeObject}
          nodeThreeObjectExtend={true}
          linkOpacity={0.6}
          linkColor={() => 'rgba(100,116,139,0.8)'}
          backgroundColor="rgba(255,255,255,0)"
          onNodeClick={handleNodeClick}
          onNodeHover={handleNodeHover}
          enableNodeDrag={true}
          onNodeDrag={() => {
            /* no-op, but keeps drag active */
          }}
          onNodeDragEnd={() => {
            /* no-op */
          }}
        />
      </Suspense>
      <div className="pointer-events-none absolute bottom-4 left-4 z-20 flex flex-col gap-2">
        <Button
          variant="secondary"
          size="icon"
          aria-label="Zoom in"
          className="pointer-events-auto h-9 w-9 bg-white/95 text-slate-700 shadow-sm hover:bg-white"
          onClick={() => handleZoomStep('in')}
        >
          +
        </Button>
        <Button
          variant="secondary"
          size="icon"
          aria-label="Zoom out"
          className="pointer-events-auto h-9 w-9 bg-white/95 text-slate-700 shadow-sm hover:bg-white"
          onClick={() => handleZoomStep('out')}
        >
          -
        </Button>
        <Button
          variant="secondary"
          size="sm"
          aria-label="Reset view"
          className="pointer-events-auto bg-white/95 text-slate-700 shadow-sm hover:bg-white"
          onClick={handleResetView}
        >
          Reset
        </Button>
      </div>
    </div>
  )
}

export default React.memo(ForceGraph3DCanvas)
