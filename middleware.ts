import { auth } from '@/auth-edge'
import { NextResponse } from 'next/server'
import { LOCALES } from '@/app/i18n/config'

// Protected route prefixes (require auth)
const PROTECTED_PREFIXES = [
  '/conversations',
  '/users',
  '/feedback',
  '/subscription',
  '/dragTree',
  '/screening',
]

function stripLocalePrefix(pathname: string): {
  locale: string | null
  path: string
} {
  for (const locale of LOCALES) {
    const prefix = `/${locale}`
    if (pathname === prefix) {
      return { locale, path: '/' }
    }
    if (pathname.startsWith(prefix + '/')) {
      return { locale, path: pathname.slice(prefix.length) || '/' }
    }
  }
  return { locale: null, path: pathname }
}

export default auth(req => {
  const url = req.nextUrl.clone()

  // Handle locale-prefixed URLs by rewriting to the underlying route
  const { locale, path } = stripLocalePrefix(url.pathname)
  if (locale) {
    url.pathname = path
  }

  // Enforce auth only for protected prefixes
  const requiresAuth = PROTECTED_PREFIXES.some(prefix =>
    url.pathname.startsWith(prefix)
  )
  if (requiresAuth && !req.auth) {
    const signInUrl = new URL('/', req.nextUrl.origin)
    return NextResponse.redirect(signInUrl)
  }

  // If we stripped a locale, rewrite to the underlying path so existing routes work
  if (locale) {
    return NextResponse.rewrite(url)
  }

  // Otherwise continue
  return NextResponse.next()
})

export const config = {
  matcher: [
    // Run on locale-prefixed paths so we can rewrite them
    '/(zh-Hans|zh-Hant|ja|es|en)/:path*',
    // And on protected base paths for auth enforcement
    '/conversations/:path*',
    '/users/:path*',
    '/feedback/:path*',
    '/subscription/:path*',
    '/dragTree/:path*',
    '/screening/:path*',
  ],
}
